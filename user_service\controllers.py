"""
Controllers for the User Service.

This module contains the business logic for user management.

English: This file contains the logic for user registration and management
Tanglish: Indha file-la user registration and management-kku logic irukku
"""

from flask import jsonify, request
from user_service.common.utils import handle_error, validate_request_data
from user_service.models import User
from user_service.common.db_config import db

def register_user():
    """
    Register a new user.

    Returns:
        JSON response with the created user

    English: This function registers a new user
    Tanglish: Indha function puthusa oru user-a register pannum
    """
    # Get request data
    data = request.get_json()
    if not data:
        return handle_error("No data provided", 400)

    # Check if required fields are present
    required_fields = ['username', 'password', 'email', 'role']
    missing_fields = [field for field in required_fields if field not in data]
    if missing_fields:
        return handle_error(f"Missing required fields: {', '.join(missing_fields)}", 400)

    # Get user role from request environment
    user_role = request.environ.get('user_role')

    print(f"User role from environment: '{user_role}'")
    print(f"Attempting to register a user with role: '{data['role']}'")

    # Check if this is a Super Admin registration and if there are no users yet
    is_first_super_admin = data['role'] == 'Super Admin' and User.query.count() == 0

    # Skip permission check for the first Super Admin
    if not is_first_super_admin and not has_permission_to_register(user_role, data['role']):
        return handle_error(f"You don't have permission to register a {data['role']}", 403)

    # Check if username or email already exists
    if User.query.filter_by(username=data['username']).first():
        return handle_error("Username already exists", 400)

    if User.query.filter_by(email=data['email']).first():
        return handle_error("Email already exists", 400)

    # Create new user
    is_admin = data.get('is_admin', False)

    user = User(
        username=data['username'],
        password=data['password'],
        email=data['email'],
        role=data['role'],
        is_admin=is_admin,
        course=data.get('course'),
        main_code=data.get('main_code'),
        reset_otp=data.get('reset_otp'),
        reset_otp_expires=data.get('reset_otp_expires')
    )

    # Save user to database
    db.session.add(user)
    db.session.commit()

    # Return user information
    return jsonify({
        "message": "User registered successfully",
        "user": user.to_dict()
    }), 201

def get_users():
    """
    Get all users.

    Returns:
        JSON response with all users

    English: This function gets all users
    Tanglish: Indha function ella users-um get pannum
    """
    # Get user role from request environment
    user_role = request.environ.get('user_role')
    print(f"Controller: User role from request environment: '{user_role}'")

    # Check if the user has permission to view all users
    # Convert roles to lowercase for case-insensitive comparison
    allowed_roles = ['super admin', 'admin']
    user_role_lower = user_role.lower() if user_role else ""

    print(f"Checking if '{user_role_lower}' is in {allowed_roles}")

    if user_role_lower not in allowed_roles:
        print(f"Controller: Permission denied. Role '{user_role}' ('{user_role_lower}') not in {allowed_roles}")
        return handle_error("You don't have permission to view all users", 403)

    print(f"Controller: Permission granted. Role '{user_role}' is in ['Super Admin', 'Admin']")

    # Get all users
    users = User.query.all()

    # Return users
    return jsonify({
        "users": [user.to_dict() for user in users]
    })

def get_user(user_id):
    """
    Get a specific user.

    Args:
        user_id: ID of the user to get

    Returns:
        JSON response with the user

    English: This function gets a specific user
    Tanglish: Indha function specific user-a get pannum
    """
    # Get user role from request environment
    user_role = request.environ.get('user_role')
    user_id_from_token = request.environ.get('user_id')

    # Check if the user has permission to view this user
    # Convert roles to lowercase for case-insensitive comparison
    allowed_roles = ['super admin', 'admin']
    user_role_lower = user_role.lower() if user_role else ""

    if user_role_lower not in allowed_roles and str(user_id_from_token) != str(user_id):
        print(f"Permission denied. Role '{user_role}' ('{user_role_lower}') not in {allowed_roles} and user_id_from_token ({user_id_from_token}) != user_id ({user_id})")
        return handle_error("You don't have permission to view this user", 403)

    # Get the user
    user = User.query.get(user_id)
    if not user:
        return handle_error("User not found", 404)

    # Return user
    return jsonify({
        "user": user.to_dict()
    })

def has_permission_to_register(user_role, target_role):
    """
    Check if a user has permission to register a user with a specific role.

    Args:
        user_role: Role of the user making the request
        target_role: Role of the user being registered

    Returns:
        True if the user has permission, False otherwise

    English: This function checks if a user can register another user with a specific role
    Tanglish: Indha function oru user vera oru specific role-oda user-a register panna mudiyuma nu check pannum
    """
    # If no user role is provided (first registration or exempt route), allow it
    if not user_role:
        print("No user role provided, allowing registration")
        return True

    # Convert roles to lowercase for case-insensitive comparison
    user_role_lower = user_role.lower() if user_role else ""
    target_role_lower = target_role.lower() if target_role else ""

    # Role hierarchy with lowercase keys and values
    role_hierarchy = {
        'super admin': ['admin', 'teacher', 'student', 'parent'],
        'admin': ['teacher', 'student', 'parent'],
        'teacher': ['student', 'parent']
    }

    print(f"Checking if '{user_role_lower}' can register '{target_role_lower}'")

    # Special case for Super Admin - can register any role
    if user_role_lower == 'super admin':
        print(f"Super Admin can register any role. Permission granted.")
        return True

    # Check if the user has permission based on role hierarchy
    if user_role_lower in role_hierarchy and target_role_lower in role_hierarchy.get(user_role_lower, []):
        print(f"Permission granted. '{user_role_lower}' can register '{target_role_lower}'")
        return True

    print(f"Permission denied. '{user_role_lower}' cannot register '{target_role_lower}'")
    return False
