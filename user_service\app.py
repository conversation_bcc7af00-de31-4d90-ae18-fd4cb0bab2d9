"""
Main application file for the User Service.

This module initializes and configures the Flask application for the User Service.

English: This file sets up the Flask app for the User Service
Tanglish: Indha file User Service-kku Flask app-a setup pannum
"""

import os
from flask import Flask
from flask_cors import CORS
from user_service.common.db_config import init_db
from common.middleware import JWTMiddleware
from common.logger import setup_logger
from user_service.views import user_bp
from user_service.config import get_config

def create_app():
    """
    Create and configure the Flask application.

    Returns:
        Configured Flask application

    English: This function creates and configures the Flask app
    Tanglish: Indha function Flask app-a create panni configure pannum
    """
    # Create Flask app
    app = Flask(__name__)

    # Load configuration
    app.config.from_object(get_config())

    # Enable CORS with specific configuration
    CORS(app,
         resources={r"/*": {"origins": ["http://localhost:5173", "http://localhost:5174", "http://127.0.0.1:5173"]}},
         methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
         allow_headers=["Content-Type", "Authorization", "X-Requested-With", "Accept",
                       "Origin", "Access-Control-Request-Method", "Access-Control-Request-Headers"],
         supports_credentials=True,
         expose_headers=["Content-Type", "Authorization"],
         max_age=3600)

    # Initialize database
    init_db(app, 'user')

    # Register blueprints
    app.register_blueprint(user_bp, url_prefix='/api/users')

    # Set up logger
    logger = setup_logger('user_service')

    # Apply middleware
    # Define role-based access control
    required_roles = {
        '/api/users/register': ['Super Admin', 'Admin', 'Teacher'],
        '/api/users/users': ['Super Admin', 'Admin']
    }

    # Apply middleware
    app.wsgi_app = JWTMiddleware(
        app.wsgi_app,
        exempt_routes=['/api/users/health', '/api/users/register'],
        required_roles=required_roles
    )

    return app

if __name__ == '__main__':
    # Get port from environment variable or use default
    port = int(os.environ.get('USER_SERVICE_PORT', 5001))

    # Create and run the app
    app = create_app()
    print("User Service app created, starting server...")
    app.run(host='0.0.0.0', port=port, use_reloader=False)
